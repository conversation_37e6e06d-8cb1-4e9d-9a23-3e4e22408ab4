*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:52:42] 




[17:52:42] Extension host agent started.
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.copilot-1.328.1602'
    at async Object.readdir (node:internal/fs/promises:952:18)
    at async SC (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:43:4567)
    at async Object.Ah (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:43:4496)
    at async Ou.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:67:16002)
    at async Sl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:64:51815)
    at async Sl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:64:50931)
    at async yf.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:55:25227)
    at async Promise.all (index 2)
    at async yf.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:55:20374)
    at async R8.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:55:9952)
    at async R8.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.copilot-1.328.1602'
}
[17:52:43] [<unknown>][947f3152][ExtensionHostConnection] New connection established.
[17:52:43] [<unknown>][07ee4c58][ManagementConnection] New connection established.
[17:52:43] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
[17:52:43] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.328.1602
[17:52:43] [<unknown>][947f3152][ExtensionHostConnection] <7298> Launched Extension Host Process.
[17:52:43] ComputeTargetPlatform: linux-x64
[17:52:46] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[17:57:42] New EH opened, aborting shutdown

2025-06-04 17:39:52.356 [info] [main] Log level: Info
2025-06-04 17:39:52.356 [info] [main] Validating found git in: "git"
2025-06-04 17:39:52.356 [info] [main] Using git "2.47.2" from "git"
2025-06-04 17:39:52.356 [info] [Model][doInitialScan] Initial repository scan started
2025-06-04 17:39:52.356 [info] > git rev-parse --show-toplevel [57ms]
2025-06-04 17:39:52.356 [info] > git rev-parse --git-dir --git-common-dir [689ms]
2025-06-04 17:39:52.356 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-04 17:39:52.356 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-04 17:39:52.356 [info] > git config --get commit.template [764ms]
2025-06-04 17:39:52.787 [info] > git fetch [3041ms]
2025-06-04 17:39:52.787 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-04 17:39:52.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [471ms]
2025-06-04 17:39:52.831 [info] > git config --get commit.template [6ms]
2025-06-04 17:39:53.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [669ms]
2025-06-04 17:39:54.733 [info] > git config --get commit.template [1178ms]
2025-06-04 17:39:54.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1252ms]
2025-06-04 17:39:55.020 [info] > git rev-parse --show-toplevel [274ms]
2025-06-04 17:39:55.020 [info] > git config --get --local branch.main.vscode-merge-base [280ms]
2025-06-04 17:39:55.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [785ms]
2025-06-04 17:39:55.952 [info] > git rev-parse --show-toplevel [282ms]
2025-06-04 17:39:55.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [917ms]
2025-06-04 17:39:55.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [933ms]
2025-06-04 17:39:56.032 [info] > git merge-base refs/heads/main refs/remotes/origin/main [63ms]
2025-06-04 17:39:56.039 [info] > git config --get --local branch.main.vscode-merge-base [79ms]
2025-06-04 17:39:56.072 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [33ms]
2025-06-04 17:39:56.073 [info] > git rev-parse --show-toplevel [63ms]
2025-06-04 17:39:56.138 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [95ms]
2025-06-04 17:39:56.254 [info] > git status -z -uall [125ms]
2025-06-04 17:39:56.347 [info] > git rev-parse --show-toplevel [200ms]
2025-06-04 17:39:56.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [211ms]
2025-06-04 17:39:57.117 [info] > git rev-parse --show-toplevel [661ms]
2025-06-04 17:39:57.346 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [1168ms]
2025-06-04 17:39:57.355 [info] > git rev-parse --show-toplevel [50ms]
2025-06-04 17:39:57.650 [info] > git rev-parse --show-toplevel [286ms]
2025-06-04 17:40:00.344 [info] > git check-ignore -v -z --stdin [271ms]
2025-06-04 17:40:00.362 [info] > git rev-parse --show-toplevel [275ms]
2025-06-04 17:40:00.384 [info] > git config --get commit.template [51ms]
2025-06-04 17:40:00.540 [info] > git rev-parse --show-toplevel [171ms]
2025-06-04 17:40:01.038 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-04 17:40:01.039 [info] > git rev-parse --show-toplevel [219ms]
2025-06-04 17:40:01.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [305ms]
2025-06-04 17:40:01.117 [info] > git rev-parse --show-toplevel [73ms]
2025-06-04 17:40:01.229 [info] > git status -z -uall [103ms]
2025-06-04 17:40:01.229 [info] > git rev-parse --show-toplevel [82ms]
2025-06-04 17:40:01.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [92ms]
2025-06-04 17:40:01.835 [info] > git rev-parse --show-toplevel [504ms]
2025-06-04 17:40:03.029 [info] > git rev-parse --show-toplevel [914ms]
2025-06-04 17:40:03.222 [info] > git rev-parse --show-toplevel [79ms]
2025-06-04 17:40:03.222 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-04 17:40:04.123 [info] > git config --get commit.template [868ms]
2025-06-04 17:40:04.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [615ms]
2025-06-04 17:40:04.826 [info] > git add -A -- .dockerignore .env.example .replit Dockerfile Dockerfile.prod FIXES_SUMMARY.md README.md client/.env.example client/src/components/ai/AIConfigurationSection.tsx client/src/components/auth/SignInForm.tsx client/src/components/auth/SignUpForm.tsx client/src/components/dashboard/DashboardOverview.tsx client/src/components/dashboard/SRSDashboard.tsx client/src/components/dashboard/StudySection.tsx client/src/components/dashboard/UploadSection.tsx client/src/components/document/DocumentList.tsx client/src/components/document/DocumentViewer.tsx client/src/components/document/EnhancedDocumentUpload.tsx client/src/components/document/InlineDocumentViewer.tsx client/src/components/document/MarkdownRenderer.tsx client/src/components/export/ExportSection.tsx client/src/components/flashcards/AiFlashcardGenerator.tsx client/src/components/flashcards/CreateFlashcardSetForm.tsx client/src/components/flashcards/FlashcardEditManager.tsx client/src/components/flashcards/FlashcardForm.tsx client/src/components/flashcards/FlashcardGenerationPopup.tsx client/src/components/flashcards/FlashcardItem.tsx client/src/components/flashcards/FlashcardManager.tsx client/src/components/flashcards/FlashcardReviewSection.tsx client/src/components/flashcards/FlashcardSetList.tsx client/src/components/flashcards/FlashcardsList.tsx client/src/components/flashcards/QuizGenerationPopup.tsx client/src/components/layout/MobileNav.tsx client/src/components/quiz/AiQuestionGenerator.tsx client/src/components/quiz/CreateQuizForm.tsx client/src/components/quiz/DashboardQuizGenerationPopup.tsx client/src/components/quiz/QuestionForm.tsx client/src/components/quiz/QuestionsList.tsx client/src/components/quiz/QuizList.tsx client/src/components/quiz/QuizPlayer.tsx client/src/components/quiz/QuizQuestionManager.tsx client/src/components/quiz/QuizSettingsToggle.tsx client/src/components/quiz/SRSQuizMode.tsx client/src/components/ui/Spinner.tsx client/src/components/ui/ThemeToggle.tsx client/src/hooks/useAuth.tsx client/src/hooks/useDocuments.tsx client/src/hooks/useSRSStats.ts client/src/lib/ai-provider.ts client/src/lib/api.ts client/src/lib/api/quizApi.ts client/src/lib/dataSync.ts client/src/lib/file-parser.ts client/src/lib/notifications.ts client/src/lib/storage.ts client/src/lib/supabaseClient.ts client/src/pages/Dashboard.tsx client/src/pages/DashboardPage.tsx client/src/pages/DocumentViewPage.tsx client/src/pages/FlashcardEditPage.tsx client/src/pages/FlashcardReview.tsx client/src/pages/FlashcardsPage.tsx client/src/pages/QuizEditPage.tsx client/src/pages/TestPage.tsx docker-compose.dev.yml docker-compose.prod.yml docker-start.sh env.example package-lock.json package.json run_dev.sh run_production.sh server/config.ts server/index.ts server/middleware/supabaseMiddleware.ts server/routes.ts server/routes/aiRoutes.ts server/routes/flashcardRoutes.ts server/routes/flashcardSetRoutes.ts server/routes/flashcards.ts server/routes/quizExpressRoutes.ts server/routes/quizRoutes.ts server/vite.ts tsconfig.json vite.config.ts client/src/components/common/ErrorBoundary.tsx confirm-user.js docs/API.md docs/DEPLOYMENT.md docs/MEMORIES.md docs/PRODUCTION_SETUP_SUMMARY.md docs/README.md docs/RULES.md docs/SECURITY.md docs/SECURITY_AUDIT_REPORT.md docs/SECURITY_MIGRATION_PLAN.md docs/TROUBLESHOOTING.md scripts/update-supabase-auth.js server/debug/checkCredentials.ts server/middleware/apiKeyStorage.ts server/routes/authRoutes.ts server/routes/credentialsRoutes.ts server/tsconfig.json start-dev-clean.sh start-server-clean.js supabase/migrations/20250602_create_user_ai_credentials.sql [3603ms]
2025-06-04 17:40:05.085 [info] > git config --get commit.template [223ms]
2025-06-04 17:40:05.137 [info] > git config --get --local branch.main.github-pr-owner-number [204ms]
2025-06-04 17:40:05.137 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:40:05.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [189ms]
2025-06-04 17:40:05.350 [info] > git status -z -uall [19ms]
2025-06-04 17:40:05.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-04 17:40:06.741 [info] > git config --get commit.template [314ms]
2025-06-04 17:40:08.223 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1399ms]
2025-06-04 17:40:08.257 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [226ms]
2025-06-04 17:40:08.257 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-04 17:40:08.422 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [105ms]
2025-06-04 17:40:08.731 [info] > git status -z -uall [275ms]
2025-06-04 17:40:08.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [206ms]
2025-06-04 17:40:11.722 [info] > git -c user.useConfigOnly=true commit --quiet --allow-empty-message --file - [86ms]
2025-06-04 17:40:11.722 [info] Author identity unknown

*** Please tell me who you are.

Run

  git config --global user.email "<EMAIL>"
  git config --global user.name "Your Name"

to set your account's default identity.
Omit --global to set the identity only in this repository.

fatal: no email was given and auto-detection is disabled
2025-06-04 17:40:11.826 [info] > git config --get-all user.name [90ms]
2025-06-04 17:40:11.842 [info] > git config --get commit.template [1ms]
2025-06-04 17:40:11.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-04 17:40:12.035 [info] > git status -z -uall [94ms]
2025-06-04 17:40:12.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-04 17:40:14.118 [info] > git config --get commit.template [2ms]
2025-06-04 17:40:14.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [24ms]
2025-06-04 17:40:14.834 [info] > git status -z -uall [17ms]
2025-06-04 17:40:14.834 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-04 17:40:19.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:40:19.864 [info] > git config --get commit.template [16ms]
2025-06-04 17:40:19.888 [info] > git status -z -uall [10ms]
2025-06-04 17:40:19.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-04 17:40:24.923 [info] > git config --get commit.template [13ms]
2025-06-04 17:40:24.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-04 17:40:24.964 [info] > git status -z -uall [19ms]
2025-06-04 17:40:24.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:40:29.996 [info] > git config --get commit.template [16ms]
2025-06-04 17:40:29.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:40:30.015 [info] > git status -z -uall [7ms]
2025-06-04 17:40:30.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:35.035 [info] > git config --get commit.template [8ms]
2025-06-04 17:40:35.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:40:35.047 [info] > git status -z -uall [5ms]
2025-06-04 17:40:35.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:40.067 [info] > git config --get commit.template [6ms]
2025-06-04 17:40:40.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:40:40.083 [info] > git status -z -uall [6ms]
2025-06-04 17:40:40.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:45.105 [info] > git config --get commit.template [8ms]
2025-06-04 17:40:45.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:40:45.119 [info] > git status -z -uall [7ms]
2025-06-04 17:40:45.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:48.767 [info] > git config --get commit.template [10ms]
2025-06-04 17:40:48.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:40:48.785 [info] > git status -z -uall [8ms]
2025-06-04 17:40:48.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:40:48.935 [info] > git merge-base refs/heads/main refs/remotes/origin/main [138ms]
2025-06-04 17:40:48.949 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [3ms]
2025-06-04 17:40:49.061 [info] > git config --get --local branch.main.github-pr-owner-number [1ms]
2025-06-04 17:40:49.062 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:40:49.162 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [114ms]
2025-06-04 17:40:50.393 [info] > git config --get commit.template [3ms]
2025-06-04 17:40:50.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-04 17:40:50.440 [info] > git status -z -uall [10ms]
2025-06-04 17:40:50.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:51.638 [info] > git log --oneline --cherry main...main@{upstream} -- [8ms]
2025-06-04 17:40:54.792 [info] > git pull --tags origin main [3076ms]
2025-06-04 17:40:54.793 [info] From https://github.com/Chewy42/ChewyAI
 * branch            main       -> FETCH_HEAD
   435a5ff..84b9e81  main       -> origin/main
hint: You have divergent branches and need to specify how to reconcile them.
hint: You can do so by running one of the following commands sometime before
hint: your next pull:
hint:
hint:   git config pull.rebase false  # merge
hint:   git config pull.rebase true   # rebase
hint:   git config pull.ff only       # fast-forward only
hint:
hint: You can replace "git config" with "git config --global" to set a default
hint: preference for all repositories. You can also pass --rebase, --no-rebase,
hint: or --ff-only on the command line to override the configured default per
hint: invocation.
fatal: Need to specify how to reconcile divergent branches.
2025-06-04 17:40:54.848 [info] > git config --get commit.template [27ms]
2025-06-04 17:40:54.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-04 17:40:54.877 [info] > git status -z -uall [6ms]
2025-06-04 17:40:54.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:40:54.893 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-04 17:40:54.901 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [2ms]
2025-06-04 17:40:55.005 [info] > git config --get --local branch.main.github-pr-owner-number [7ms]
2025-06-04 17:40:55.005 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:40:55.134 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [149ms]
2025-06-04 17:40:56.961 [info] > git config --get commit.template [7ms]
2025-06-04 17:40:56.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:40:56.977 [info] > git status -z -uall [8ms]
2025-06-04 17:40:56.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:02.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-04 17:41:02.006 [info] > git config --get commit.template [15ms]
2025-06-04 17:41:02.023 [info] > git status -z -uall [8ms]
2025-06-04 17:41:02.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:41:07.046 [info] > git config --get commit.template [9ms]
2025-06-04 17:41:07.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:41:07.063 [info] > git status -z -uall [7ms]
2025-06-04 17:41:07.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:07.256 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [79ms]
2025-06-04 17:41:12.083 [info] > git config --get commit.template [7ms]
2025-06-04 17:41:12.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:41:12.096 [info] > git status -z -uall [6ms]
2025-06-04 17:41:12.097 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:17.116 [info] > git config --get commit.template [9ms]
2025-06-04 17:41:17.118 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:41:17.132 [info] > git status -z -uall [8ms]
2025-06-04 17:41:17.134 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:41:22.153 [info] > git config --get commit.template [7ms]
2025-06-04 17:41:22.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:41:22.171 [info] > git status -z -uall [9ms]
2025-06-04 17:41:22.173 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:27.341 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [101ms]
2025-06-04 17:41:27.524 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [193ms]
2025-06-04 17:41:27.922 [info] > git status -z -uall [295ms]
2025-06-04 17:41:27.922 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-04 17:41:28.256 [info] > git config --get --local branch.undefined.remote [2ms]
2025-06-04 17:41:28.256 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:41:28.316 [info] > git config --get --local branch.undefined.merge [9ms]
2025-06-04 17:41:28.316 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:41:28.360 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [127ms]
2025-06-04 17:41:28.769 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-04 17:41:32.945 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [7ms]
2025-06-04 17:41:32.970 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [25ms]
2025-06-04 17:41:32.984 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:32.987 [info] > git status -z -uall [9ms]
2025-06-04 17:41:37.155 [info] > git check-ignore -v -z --stdin [413ms]
2025-06-04 17:41:37.161 [info] > git diff --name-status -z --diff-filter=ADMR REBASE_HEAD...HEAD [13ms]
2025-06-04 17:41:37.162 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z HEAD -- [36ms]
2025-06-04 17:41:37.162 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z REBASE_HEAD -- [28ms]
2025-06-04 17:41:37.743 [info] > git show --textconv :1:client/src/lib/api.ts [406ms]
2025-06-04 17:41:37.743 [info] > git show --textconv 84b9e81d6e847f2506f8a35f2c8325979eeca42c:client/src/lib/api.ts [317ms]
2025-06-04 17:41:37.743 [info] > git show --textconv a965f86a6f36b671c6d2b2f0c02eb8d425aa309d:client/src/lib/api.ts [228ms]
2025-06-04 17:41:37.743 [info] > git ls-tree -l a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- client/src/lib/api.ts [115ms]
2025-06-04 17:41:37.816 [info] > git ls-tree -l 84b9e81d6e847f2506f8a35f2c8325979eeca42c -- client/src/lib/api.ts [197ms]
2025-06-04 17:41:37.816 [info] > git ls-files --stage -- client/src/lib/api.ts [286ms]
2025-06-04 17:41:38.821 [info] > git cat-file -s 46737e1c6f82a208a09befa57f1d42140f720549 [992ms]
2025-06-04 17:41:40.430 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [189ms]
2025-06-04 17:41:40.517 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [197ms]
2025-06-04 17:41:40.826 [info] > git status -z -uall [211ms]
2025-06-04 17:41:40.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [122ms]
2025-06-04 17:41:42.033 [info] > git check-ignore -v -z --stdin [382ms]
2025-06-04 17:41:42.418 [info] > git check-ignore -v -z --stdin [199ms]
2025-06-04 17:41:45.937 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [9ms]
2025-06-04 17:41:45.965 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [28ms]
2025-06-04 17:41:45.985 [info] > git status -z -uall [10ms]
2025-06-04 17:41:45.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:51.050 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [11ms]
2025-06-04 17:41:51.141 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [91ms]
2025-06-04 17:41:51.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:41:51.229 [info] > git status -z -uall [76ms]
2025-06-04 17:42:05.748 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [1084ms]
2025-06-04 17:42:05.749 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [1097ms]
2025-06-04 17:42:05.825 [info] > git status -z -uall [64ms]
2025-06-04 17:42:05.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-04 17:42:09.085 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1058ms]
2025-06-04 17:42:10.340 [info] > git add -A -- client/src/lib/api.ts [9ms]
2025-06-04 17:42:10.361 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [8ms]
2025-06-04 17:42:10.440 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [79ms]
2025-06-04 17:42:10.531 [info] > git status -z -uall [15ms]
2025-06-04 17:42:10.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:42:10.848 [info] > git for-each-ref --format %(refname)%00%(objectname)%00%(*objectname) refs/tags [6ms]
2025-06-04 17:42:10.926 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- [78ms]
2025-06-04 17:42:10.952 [info] > git status -z -uall [13ms]
2025-06-04 17:42:10.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:42:11.135 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-04 17:42:11.853 [info] > git ls-tree -l a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- client/src/lib/api.ts [3ms]
2025-06-04 17:42:11.854 [info] > git ls-tree -l 84b9e81d6e847f2506f8a35f2c8325979eeca42c -- client/src/lib/api.ts [15ms]
2025-06-04 17:42:11.854 [info] > git ls-files --stage -- client/src/lib/api.ts [26ms]
2025-06-04 17:42:11.868 [info] > git cat-file -s 2491ff56d3eeae7d655f61171a3c0e42b9b89680 [2ms]
2025-06-04 17:42:11.961 [info] > git show --textconv a965f86a6f36b671c6d2b2f0c02eb8d425aa309d:client/src/lib/api.ts [19ms]
2025-06-04 17:42:11.961 [info] > git show --textconv 84b9e81d6e847f2506f8a35f2c8325979eeca42c:client/src/lib/api.ts [9ms]
2025-06-04 17:42:11.963 [info] > git show --textconv :1:client/src/lib/api.ts [2ms]
2025-06-04 17:42:12.027 [info] > git hash-object -t tree /dev/null [3ms]
2025-06-04 17:42:12.027 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/client/src/lib/api.ts?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fclient%2Fsrc%2Flib%2Fapi.ts%22%2C%22ref%22%3A%22%3A1%22%7D
2025-06-04 17:42:12.524 [info] > git rebase --continue [148ms]
2025-06-04 17:42:12.524 [info] Successfully rebased and updated refs/heads/main.
2025-06-04 17:42:12.540 [info] > git config --get commit.template [2ms]
2025-06-04 17:42:12.567 [info] > git config --get commit.template [14ms]
2025-06-04 17:42:12.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:42:12.690 [info] > git status -z -uall [65ms]
2025-06-04 17:42:12.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [78ms]
2025-06-04 17:42:12.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [122ms]
2025-06-04 17:42:12.869 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-04 17:42:12.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [13ms]
2025-06-04 17:42:12.934 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-04 17:42:12.950 [info] > git config --get --local branch.main.github-pr-owner-number [17ms]
2025-06-04 17:42:12.950 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:42:12.976 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c...refs/remotes/origin/main [29ms]
2025-06-04 17:42:13.269 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [246ms]
2025-06-04 17:42:13.552 [info] > git log --oneline --cherry main...main@{upstream} -- [3ms]
2025-06-04 17:42:14.634 [info] > git pull --tags origin main [1072ms]
2025-06-04 17:42:14.634 [info] From https://github.com/Chewy42/ChewyAI
 * branch            main       -> FETCH_HEAD
2025-06-04 17:42:16.168 [info] > git ls-tree -l 84b9e81d6e847f2506f8a35f2c8325979eeca42c -- client/src/lib/api.ts [13ms]
2025-06-04 17:42:16.168 [info] > git ls-files --stage -- client/src/lib/api.ts [26ms]
2025-06-04 17:42:16.176 [info] > git ls-tree -l a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- client/src/lib/api.ts [9ms]
2025-06-04 17:42:16.177 [info] > git cat-file -s 2491ff56d3eeae7d655f61171a3c0e42b9b89680 [2ms]
2025-06-04 17:42:16.284 [info] > git show --textconv 84b9e81d6e847f2506f8a35f2c8325979eeca42c:client/src/lib/api.ts [31ms]
2025-06-04 17:42:16.284 [info] > git show --textconv a965f86a6f36b671c6d2b2f0c02eb8d425aa309d:client/src/lib/api.ts [11ms]
2025-06-04 17:42:16.284 [info] > git show --textconv :1:client/src/lib/api.ts [2ms]
2025-06-04 17:42:16.285 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/client/src/lib/api.ts?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fclient%2Fsrc%2Flib%2Fapi.ts%22%2C%22ref%22%3A%22%3A1%22%7D
2025-06-04 17:42:16.370 [info] > git push origin main:main [1720ms]
2025-06-04 17:42:16.370 [info] remote: 
remote: GitHub found 8 vulnerabilities on Chewy42/ChewyAI's default branch (1 high, 7 moderate). To find out more, visit:        
remote:      https://github.com/Chewy42/ChewyAI/security/dependabot        
remote: 
To https://github.com/Chewy42/ChewyAI
   84b9e81..c0f3c70  main -> main
2025-06-04 17:42:16.430 [info] > git config --get commit.template [7ms]
2025-06-04 17:42:16.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:42:16.480 [info] > git status -z -uall [11ms]
2025-06-04 17:42:16.484 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:42:16.547 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-04 17:42:16.559 [info] > git config --get commit.template [13ms]
2025-06-04 17:42:16.572 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267...refs/remotes/origin/main [14ms]
2025-06-04 17:42:16.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:42:16.669 [info] > git status -z -uall [47ms]
2025-06-04 17:42:16.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [32ms]
2025-06-04 17:42:16.672 [info] > git config --get --local branch.main.github-pr-owner-number [3ms]
2025-06-04 17:42:16.672 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:42:16.927 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [270ms]
2025-06-04 17:42:17.850 [info] > git ls-tree -l a965f86a6f36b671c6d2b2f0c02eb8d425aa309d -- client/src/lib/api.ts [6ms]
2025-06-04 17:42:17.850 [info] > git ls-tree -l 84b9e81d6e847f2506f8a35f2c8325979eeca42c -- client/src/lib/api.ts [12ms]
2025-06-04 17:42:17.851 [info] > git ls-files --stage -- client/src/lib/api.ts [22ms]
2025-06-04 17:42:17.864 [info] > git cat-file -s 2491ff56d3eeae7d655f61171a3c0e42b9b89680 [2ms]
2025-06-04 17:42:18.044 [info] > git show --textconv a965f86a6f36b671c6d2b2f0c02eb8d425aa309d:client/src/lib/api.ts [80ms]
2025-06-04 17:42:18.044 [info] > git show --textconv 84b9e81d6e847f2506f8a35f2c8325979eeca42c:client/src/lib/api.ts [20ms]
2025-06-04 17:42:18.045 [info] > git show --textconv :1:client/src/lib/api.ts [5ms]
2025-06-04 17:42:18.048 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/client/src/lib/api.ts?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2Fclient%2Fsrc%2Flib%2Fapi.ts%22%2C%22ref%22%3A%22%3A1%22%7D
2025-06-04 17:42:21.693 [info] > git config --get commit.template [9ms]
2025-06-04 17:42:21.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:42:21.708 [info] > git status -z -uall [7ms]
2025-06-04 17:42:21.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:43:03.623 [info] > git config --get commit.template [14ms]
2025-06-04 17:43:03.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-04 17:43:03.658 [info] > git status -z -uall [14ms]
2025-06-04 17:43:03.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-04 17:44:08.090 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [58ms]
2025-06-04 17:44:44.719 [info] > git config --get commit.template [14ms]
2025-06-04 17:44:44.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-04 17:44:44.753 [info] > git status -z -uall [14ms]
2025-06-04 17:44:44.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 17:44:49.773 [info] > git config --get commit.template [3ms]
2025-06-04 17:44:49.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-04 17:44:49.814 [info] > git status -z -uall [9ms]
2025-06-04 17:44:49.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:44:54.851 [info] > git config --get commit.template [18ms]
2025-06-04 17:44:54.852 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:44:54.879 [info] > git status -z -uall [17ms]
2025-06-04 17:44:54.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:44:55.545 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [5ms]
2025-06-04 17:44:59.913 [info] > git config --get commit.template [13ms]
2025-06-04 17:44:59.914 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:44:59.937 [info] > git status -z -uall [12ms]
2025-06-04 17:44:59.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:45:04.957 [info] > git config --get commit.template [6ms]
2025-06-04 17:45:04.957 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:45:04.981 [info] > git status -z -uall [11ms]
2025-06-04 17:45:04.982 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:45:10.006 [info] > git config --get commit.template [9ms]
2025-06-04 17:45:10.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:45:10.023 [info] > git status -z -uall [8ms]
2025-06-04 17:45:10.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:45:15.052 [info] > git config --get commit.template [13ms]
2025-06-04 17:45:15.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:45:15.070 [info] > git status -z -uall [9ms]
2025-06-04 17:45:15.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:45:20.090 [info] > git config --get commit.template [8ms]
2025-06-04 17:45:20.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:45:20.105 [info] > git status -z -uall [7ms]
2025-06-04 17:45:20.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:45:25.362 [info] > git config --get commit.template [219ms]
2025-06-04 17:45:25.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:45:25.423 [info] > git status -z -uall [21ms]
2025-06-04 17:45:25.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:45:30.457 [info] > git config --get commit.template [11ms]
2025-06-04 17:45:30.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:45:30.487 [info] > git status -z -uall [14ms]
2025-06-04 17:45:30.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:45:35.599 [info] > git config --get commit.template [24ms]
2025-06-04 17:45:35.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:45:35.662 [info] > git status -z -uall [41ms]
2025-06-04 17:45:35.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-04 17:46:06.221 [info] > git config --get commit.template [10ms]
2025-06-04 17:46:06.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:46:06.240 [info] > git status -z -uall [10ms]
2025-06-04 17:46:06.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:46:08.034 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [5ms]
2025-06-04 17:46:11.279 [info] > git config --get commit.template [18ms]
2025-06-04 17:46:11.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:46:11.321 [info] > git status -z -uall [20ms]
2025-06-04 17:46:11.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:46:16.359 [info] > git config --get commit.template [15ms]
2025-06-04 17:46:16.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:46:16.383 [info] > git status -z -uall [8ms]
2025-06-04 17:46:16.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:46:21.529 [info] > git config --get commit.template [92ms]
2025-06-04 17:46:21.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-04 17:46:21.644 [info] > git status -z -uall [94ms]
2025-06-04 17:46:21.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:46:59.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-04 17:46:59.715 [info] > git config --get commit.template [29ms]
2025-06-04 17:46:59.755 [info] > git status -z -uall [22ms]
2025-06-04 17:46:59.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 17:47:04.806 [info] > git config --get commit.template [28ms]
2025-06-04 17:47:04.810 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-04 17:47:04.891 [info] > git status -z -uall [44ms]
2025-06-04 17:47:04.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:47:09.961 [info] > git config --get commit.template [13ms]
2025-06-04 17:47:09.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:10.014 [info] > git status -z -uall [20ms]
2025-06-04 17:47:10.018 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:47:15.082 [info] > git config --get commit.template [3ms]
2025-06-04 17:47:15.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:47:15.135 [info] > git status -z -uall [17ms]
2025-06-04 17:47:15.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:47:20.202 [info] > git config --get commit.template [2ms]
2025-06-04 17:47:20.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:20.241 [info] > git status -z -uall [13ms]
2025-06-04 17:47:20.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:47:25.267 [info] > git config --get commit.template [11ms]
2025-06-04 17:47:25.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:25.282 [info] > git status -z -uall [7ms]
2025-06-04 17:47:25.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:47:30.305 [info] > git config --get commit.template [9ms]
2025-06-04 17:47:30.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:30.321 [info] > git status -z -uall [7ms]
2025-06-04 17:47:30.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:47:35.350 [info] > git config --get commit.template [14ms]
2025-06-04 17:47:35.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:35.371 [info] > git status -z -uall [10ms]
2025-06-04 17:47:35.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:47:40.398 [info] > git config --get commit.template [11ms]
2025-06-04 17:47:40.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:40.415 [info] > git status -z -uall [9ms]
2025-06-04 17:47:40.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:47:45.440 [info] > git config --get commit.template [9ms]
2025-06-04 17:47:45.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:45.459 [info] > git status -z -uall [9ms]
2025-06-04 17:47:45.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:47:50.490 [info] > git config --get commit.template [14ms]
2025-06-04 17:47:50.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:47:50.518 [info] > git status -z -uall [13ms]
2025-06-04 17:47:50.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:48:08.042 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [8ms]
2025-06-04 17:50:08.055 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [15ms]
2025-06-04 17:51:14.664 [info] > git config --get commit.template [25ms]
2025-06-04 17:51:14.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-04 17:51:14.706 [info] > git status -z -uall [19ms]
2025-06-04 17:51:14.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:51:19.732 [info] > git config --get commit.template [2ms]
2025-06-04 17:51:19.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-04 17:51:19.798 [info] > git status -z -uall [20ms]
2025-06-04 17:51:19.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:51:24.823 [info] > git config --get commit.template [1ms]
2025-06-04 17:51:24.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:51:24.882 [info] > git status -z -uall [20ms]
2025-06-04 17:51:24.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:51:29.906 [info] > git config --get commit.template [8ms]
2025-06-04 17:51:29.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:51:29.931 [info] > git status -z -uall [15ms]
2025-06-04 17:51:29.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:51:34.962 [info] > git config --get commit.template [13ms]
2025-06-04 17:51:34.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:51:34.991 [info] > git status -z -uall [14ms]
2025-06-04 17:51:34.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:51:40.020 [info] > git config --get commit.template [11ms]
2025-06-04 17:51:40.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:51:40.049 [info] > git status -z -uall [13ms]
2025-06-04 17:51:40.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:51:45.074 [info] > git config --get commit.template [2ms]
2025-06-04 17:51:45.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:51:45.136 [info] > git status -z -uall [29ms]
2025-06-04 17:51:45.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-04 17:51:50.177 [info] > git config --get commit.template [12ms]
2025-06-04 17:51:50.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:51:50.196 [info] > git status -z -uall [8ms]
2025-06-04 17:51:50.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:52:08.040 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [5ms]

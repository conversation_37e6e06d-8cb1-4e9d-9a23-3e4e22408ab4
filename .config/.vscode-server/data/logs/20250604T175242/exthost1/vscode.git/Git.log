2025-06-04 17:52:49.060 [info] [main] Log level: Info
2025-06-04 17:52:49.060 [info] [main] Validating found git in: "git"
2025-06-04 17:52:49.061 [info] [main] Using git "2.47.2" from "git"
2025-06-04 17:52:49.061 [info] [Model][doInitialScan] Initial repository scan started
2025-06-04 17:52:49.061 [info] > git rev-parse --show-toplevel [51ms]
2025-06-04 17:52:49.061 [info] > git rev-parse --git-dir --git-common-dir [97ms]
2025-06-04 17:52:49.061 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-04 17:52:49.061 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-04 17:52:49.061 [info] > git config --get commit.template [81ms]
2025-06-04 17:52:49.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [262ms]
2025-06-04 17:52:50.478 [info] > git status -z -uall [921ms]
2025-06-04 17:52:50.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [915ms]
2025-06-04 17:52:50.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-06-04 17:52:50.546 [info] > git rev-parse --show-toplevel [381ms]
2025-06-04 17:52:50.561 [info] > git config --get commit.template [30ms]
2025-06-04 17:52:50.564 [info] > git rev-parse --show-toplevel [10ms]
2025-06-04 17:52:50.564 [info] > git config --get --local branch.main.vscode-merge-base [19ms]
2025-06-04 17:52:50.863 [info] > git rev-parse --show-toplevel [281ms]
2025-06-04 17:52:50.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [288ms]
2025-06-04 17:52:50.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [293ms]
2025-06-04 17:52:50.881 [info] > git merge-base refs/heads/main refs/remotes/origin/main [10ms]
2025-06-04 17:52:50.976 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267...refs/remotes/origin/main [88ms]
2025-06-04 17:52:50.977 [info] > git rev-parse --show-toplevel [98ms]
2025-06-04 17:52:51.161 [info] > git status -z -uall [28ms]
2025-06-04 17:52:51.161 [info] > git rev-parse --show-toplevel [177ms]
2025-06-04 17:52:51.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [72ms]
2025-06-04 17:52:51.301 [info] > git rev-parse --show-toplevel [102ms]
2025-06-04 17:52:51.322 [info] > git rev-parse --show-toplevel [12ms]
2025-06-04 17:52:51.538 [info] > git rev-parse --show-toplevel [189ms]
2025-06-04 17:52:51.700 [info] > git rev-parse --show-toplevel [106ms]
2025-06-04 17:52:51.982 [info] > git rev-parse --show-toplevel [269ms]
2025-06-04 17:52:52.113 [info] > git rev-parse --show-toplevel [25ms]
2025-06-04 17:52:52.173 [info] > git rev-parse --show-toplevel [38ms]
2025-06-04 17:52:52.187 [info] > git rev-parse --show-toplevel [3ms]
2025-06-04 17:52:52.680 [info] > git rev-parse --show-toplevel [126ms]
2025-06-04 17:52:52.890 [info] > git rev-parse --show-toplevel [198ms]
2025-06-04 17:52:52.892 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-04 17:52:52.938 [info] > git config --get commit.template [17ms]
2025-06-04 17:52:52.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-04 17:52:53.031 [info] > git status -z -uall [18ms]
2025-06-04 17:52:53.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 17:52:53.402 [info] > git fetch [357ms]
2025-06-04 17:52:53.402 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-04 17:52:53.514 [info] > git config --get commit.template [102ms]
2025-06-04 17:52:53.886 [info] > git config --get --local branch.main.github-pr-owner-number [336ms]
2025-06-04 17:52:53.886 [warning] [Git][config] git config failed: Failed to execute git
2025-06-04 17:52:53.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [223ms]
2025-06-04 17:52:54.244 [info] > git status -z -uall [293ms]
2025-06-04 17:52:54.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [435ms]
2025-06-04 17:52:56.832 [info] > git config --get commit.template [452ms]
2025-06-04 17:52:57.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [550ms]
2025-06-04 17:52:57.609 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [698ms]
2025-06-04 17:52:57.779 [info] > git status -z -uall [109ms]
2025-06-04 17:52:57.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [100ms]
2025-06-04 17:53:02.813 [info] > git config --get commit.template [14ms]
2025-06-04 17:53:02.814 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:53:02.826 [info] > git status -z -uall [7ms]
2025-06-04 17:53:02.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:53:07.848 [info] > git config --get commit.template [8ms]
2025-06-04 17:53:07.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:53:07.867 [info] > git status -z -uall [8ms]
2025-06-04 17:53:07.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:53:15.149 [info] > git config --get commit.template [13ms]
2025-06-04 17:53:15.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 17:53:15.179 [info] > git status -z -uall [17ms]
2025-06-04 17:53:15.179 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-04 17:53:20.200 [info] > git config --get commit.template [7ms]
2025-06-04 17:53:20.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 17:53:20.215 [info] > git status -z -uall [6ms]
2025-06-04 17:53:20.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 17:53:25.262 [info] > git config --get commit.template [7ms]
2025-06-04 17:53:25.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:53:25.276 [info] > git status -z -uall [7ms]
2025-06-04 17:53:25.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 17:53:30.293 [info] > git config --get commit.template [2ms]
2025-06-04 17:53:30.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 17:53:30.309 [info] > git status -z -uall [4ms]
2025-06-04 17:53:30.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 17:54:56.917 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [3ms]
2025-06-04 17:56:56.918 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [4ms]
2025-06-04 17:58:56.917 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [3ms]
2025-06-04 18:00:56.929 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [4ms]
2025-06-04 18:02:56.924 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [2ms]
2025-06-04 18:04:56.930 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [11ms]
2025-06-04 18:06:56.930 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [8ms]
2025-06-04 18:08:56.920 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [2ms]
2025-06-04 18:10:56.923 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [1ms]
2025-06-04 18:12:56.925 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [3ms]
2025-06-04 18:13:16.774 [info] > git config --get commit.template [15ms]
2025-06-04 18:13:16.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-04 18:13:16.811 [info] > git status -z -uall [13ms]
2025-06-04 18:13:16.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-04 18:13:21.847 [info] > git config --get commit.template [11ms]
2025-06-04 18:13:21.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:13:21.877 [info] > git status -z -uall [13ms]
2025-06-04 18:13:21.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:13:26.900 [info] > git config --get commit.template [7ms]
2025-06-04 18:13:26.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:13:26.917 [info] > git status -z -uall [9ms]
2025-06-04 18:13:26.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 18:13:31.949 [info] > git config --get commit.template [15ms]
2025-06-04 18:13:31.950 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:13:31.977 [info] > git status -z -uall [13ms]
2025-06-04 18:13:31.978 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:13:37.014 [info] > git config --get commit.template [20ms]
2025-06-04 18:13:37.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:13:37.068 [info] > git status -z -uall [32ms]
2025-06-04 18:13:37.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:13:46.165 [info] > git config --get commit.template [14ms]
2025-06-04 18:13:46.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-04 18:13:46.212 [info] > git status -z -uall [30ms]
2025-06-04 18:13:46.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 18:13:51.260 [info] > git config --get commit.template [18ms]
2025-06-04 18:13:51.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:13:51.284 [info] > git status -z -uall [8ms]
2025-06-04 18:13:51.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:13:56.304 [info] > git config --get commit.template [1ms]
2025-06-04 18:13:56.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:13:56.356 [info] > git status -z -uall [19ms]
2025-06-04 18:13:56.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:14:01.388 [info] > git config --get commit.template [14ms]
2025-06-04 18:14:01.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:14:01.414 [info] > git status -z -uall [12ms]
2025-06-04 18:14:01.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:14:06.436 [info] > git config --get commit.template [6ms]
2025-06-04 18:14:06.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:14:06.450 [info] > git status -z -uall [7ms]
2025-06-04 18:14:06.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:14:13.447 [info] > git config --get commit.template [22ms]
2025-06-04 18:14:13.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 18:14:13.480 [info] > git status -z -uall [15ms]
2025-06-04 18:14:13.482 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:14:18.512 [info] > git config --get commit.template [13ms]
2025-06-04 18:14:18.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:14:18.533 [info] > git status -z -uall [8ms]
2025-06-04 18:14:18.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:14:23.558 [info] > git config --get commit.template [8ms]
2025-06-04 18:14:23.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:14:23.573 [info] > git status -z -uall [6ms]
2025-06-04 18:14:23.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:14:28.593 [info] > git config --get commit.template [4ms]
2025-06-04 18:14:28.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:14:28.642 [info] > git status -z -uall [23ms]
2025-06-04 18:14:28.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-04 18:14:56.923 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [2ms]
2025-06-04 18:15:25.962 [info] > git config --get commit.template [14ms]
2025-06-04 18:15:25.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:15:25.992 [info] > git status -z -uall [18ms]
2025-06-04 18:15:25.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:15:31.019 [info] > git config --get commit.template [10ms]
2025-06-04 18:15:31.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:15:31.053 [info] > git status -z -uall [19ms]
2025-06-04 18:15:31.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:15:36.085 [info] > git config --get commit.template [11ms]
2025-06-04 18:15:36.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:15:36.103 [info] > git status -z -uall [9ms]
2025-06-04 18:15:36.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:15:41.128 [info] > git config --get commit.template [10ms]
2025-06-04 18:15:41.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:15:41.147 [info] > git status -z -uall [8ms]
2025-06-04 18:15:41.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:15:46.174 [info] > git config --get commit.template [9ms]
2025-06-04 18:15:46.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:15:46.193 [info] > git status -z -uall [9ms]
2025-06-04 18:15:46.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:15:51.237 [info] > git config --get commit.template [19ms]
2025-06-04 18:15:51.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:15:51.260 [info] > git status -z -uall [10ms]
2025-06-04 18:15:51.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 18:15:56.294 [info] > git config --get commit.template [14ms]
2025-06-04 18:15:56.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 18:15:56.321 [info] > git status -z -uall [10ms]
2025-06-04 18:15:56.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:01.353 [info] > git config --get commit.template [14ms]
2025-06-04 18:16:01.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:01.389 [info] > git status -z -uall [20ms]
2025-06-04 18:16:01.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:16:06.415 [info] > git config --get commit.template [13ms]
2025-06-04 18:16:06.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:06.467 [info] > git status -z -uall [19ms]
2025-06-04 18:16:06.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 18:16:11.499 [info] > git config --get commit.template [10ms]
2025-06-04 18:16:11.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:11.518 [info] > git status -z -uall [11ms]
2025-06-04 18:16:11.520 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:16.548 [info] > git config --get commit.template [13ms]
2025-06-04 18:16:16.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:16.569 [info] > git status -z -uall [13ms]
2025-06-04 18:16:16.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:21.602 [info] > git config --get commit.template [16ms]
2025-06-04 18:16:21.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:16:21.636 [info] > git status -z -uall [16ms]
2025-06-04 18:16:21.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:16:26.662 [info] > git config --get commit.template [8ms]
2025-06-04 18:16:26.663 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:26.679 [info] > git status -z -uall [10ms]
2025-06-04 18:16:26.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:31.704 [info] > git config --get commit.template [9ms]
2025-06-04 18:16:31.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:16:31.744 [info] > git status -z -uall [23ms]
2025-06-04 18:16:31.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 18:16:36.828 [info] > git config --get commit.template [63ms]
2025-06-04 18:16:36.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [53ms]
2025-06-04 18:16:36.861 [info] > git status -z -uall [13ms]
2025-06-04 18:16:36.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:41.895 [info] > git config --get commit.template [12ms]
2025-06-04 18:16:41.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:16:41.914 [info] > git status -z -uall [9ms]
2025-06-04 18:16:41.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:46.944 [info] > git config --get commit.template [11ms]
2025-06-04 18:16:46.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-04 18:16:46.972 [info] > git status -z -uall [14ms]
2025-06-04 18:16:46.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:51.994 [info] > git config --get commit.template [7ms]
2025-06-04 18:16:51.995 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:16:52.010 [info] > git status -z -uall [6ms]
2025-06-04 18:16:52.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:16:56.932 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [8ms]
2025-06-04 18:16:57.040 [info] > git config --get commit.template [11ms]
2025-06-04 18:16:57.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:16:57.066 [info] > git status -z -uall [17ms]
2025-06-04 18:16:57.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 18:17:02.088 [info] > git config --get commit.template [6ms]
2025-06-04 18:17:02.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:17:02.113 [info] > git status -z -uall [12ms]
2025-06-04 18:17:02.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:17:07.147 [info] > git config --get commit.template [14ms]
2025-06-04 18:17:07.148 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:17:07.175 [info] > git status -z -uall [14ms]
2025-06-04 18:17:07.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:17:12.201 [info] > git config --get commit.template [7ms]
2025-06-04 18:17:12.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:17:12.215 [info] > git status -z -uall [7ms]
2025-06-04 18:17:12.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:17:17.255 [info] > git config --get commit.template [16ms]
2025-06-04 18:17:17.255 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:17:17.281 [info] > git status -z -uall [10ms]
2025-06-04 18:17:17.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:17:22.301 [info] > git config --get commit.template [3ms]
2025-06-04 18:17:22.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:17:22.339 [info] > git status -z -uall [12ms]
2025-06-04 18:17:22.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-04 18:17:27.383 [info] > git config --get commit.template [17ms]
2025-06-04 18:17:27.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:17:27.415 [info] > git status -z -uall [11ms]
2025-06-04 18:17:27.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-04 18:17:32.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:17:32.479 [info] > git config --get commit.template [35ms]
2025-06-04 18:17:32.526 [info] > git status -z -uall [28ms]
2025-06-04 18:17:32.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-04 18:18:56.925 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [2ms]
2025-06-04 18:19:28.715 [info] > git config --get commit.template [15ms]
2025-06-04 18:19:28.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:19:28.749 [info] > git status -z -uall [21ms]
2025-06-04 18:19:28.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:19:33.775 [info] > git config --get commit.template [11ms]
2025-06-04 18:19:33.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:19:33.870 [info] > git status -z -uall [83ms]
2025-06-04 18:19:33.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [68ms]
2025-06-04 18:19:38.900 [info] > git config --get commit.template [10ms]
2025-06-04 18:19:38.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:19:38.918 [info] > git status -z -uall [9ms]
2025-06-04 18:19:38.919 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:20:56.933 [info] > git diff --name-status -z --diff-filter=ADMR c0f3c7030fef8e5dd7c9f652a87b0599357e6267 [3ms]
2025-06-04 18:21:28.307 [info] > git config --get commit.template [7ms]
2025-06-04 18:21:28.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:21:28.328 [info] > git status -z -uall [11ms]
2025-06-04 18:21:28.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:21:33.357 [info] > git config --get commit.template [13ms]
2025-06-04 18:21:33.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:21:33.381 [info] > git status -z -uall [13ms]
2025-06-04 18:21:33.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:21:38.412 [info] > git config --get commit.template [14ms]
2025-06-04 18:21:38.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:21:38.442 [info] > git status -z -uall [15ms]
2025-06-04 18:21:38.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-04 18:21:43.472 [info] > git config --get commit.template [13ms]
2025-06-04 18:21:43.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [39ms]
2025-06-04 18:21:43.532 [info] > git status -z -uall [8ms]
2025-06-04 18:21:43.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:21:48.556 [info] > git config --get commit.template [8ms]
2025-06-04 18:21:48.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:21:48.583 [info] > git status -z -uall [13ms]
2025-06-04 18:21:48.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:21:53.605 [info] > git config --get commit.template [9ms]
2025-06-04 18:21:53.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:21:53.624 [info] > git status -z -uall [10ms]
2025-06-04 18:21:53.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:21:58.649 [info] > git config --get commit.template [10ms]
2025-06-04 18:21:58.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-04 18:21:58.664 [info] > git status -z -uall [6ms]
2025-06-04 18:21:58.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:22:03.686 [info] > git config --get commit.template [6ms]
2025-06-04 18:22:03.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:22:03.716 [info] > git status -z -uall [14ms]
2025-06-04 18:22:03.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-04 18:22:08.744 [info] > git config --get commit.template [11ms]
2025-06-04 18:22:08.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-04 18:22:08.759 [info] > git status -z -uall [7ms]
2025-06-04 18:22:08.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]

2025-06-04 17:52:48.794 [info] Can't use the Electron fetcher in this environment.
2025-06-04 17:52:48.794 [info] Using the Node fetch fetcher.
2025-06-04 17:52:48.795 [info] Initializing Git extension service.
2025-06-04 17:52:48.795 [info] Successfully activated the vscode.git extension.
2025-06-04 17:52:48.795 [info] Enablement state of the vscode.git extension: true.
2025-06-04 17:52:48.795 [info] Successfully registered Git commit message provider.
2025-06-04 17:52:50.995 [info] Logged in as Chewy42
2025-06-04 17:52:53.193 [info] Got Copilot token for Chewy42
2025-06-04 17:52:56.383 [info] Fetched model metadata in 3139ms 488e3035-d10c-46f1-9418-0151fb8eec26
2025-06-04 17:52:56.387 [info] activationBlocker from 'languageModelAccess' took for 7702ms
2025-06-04 17:52:56.998 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-04 17:52:57.021 [info] Registering default platform agent...
2025-06-04 17:52:57.440 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-04 17:52:57.440 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-04 17:52:57.440 [info] Successfully registered GitHub PR title and description provider.
2025-06-04 17:52:57.440 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-04 17:52:57.784 [info] TypeScript server plugin activated.
2025-06-04 17:52:57.785 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-04 17:52:57.801 [info] BYOK: Copilot Chat known models list fetched successfully.

# ChewyAI API Documentation

## 🌐 Base URL
- **Development**: `http://localhost:5000/api`
- **Production**: `https://your-replit-domain.com/api`

## 🔐 Authentication
Most endpoints require authentication using JWT tokens from Supabase Auth.

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## 🆕 Recent Updates (January 2025)

### Flashcard System Fixes
- **Critical Database Schema Fix**: Resolved 500 errors in flashcard review system caused by missing `due_at` column
- **Due Flashcards Endpoint**: Updated `/api/flashcard-sets/{id}/due` to work with current Supabase schema
- **Card Count Display**: Fixed flashcard count calculation and display issues in frontend
- **Review System**: Restored full functionality for flashcard review interface
- **Database Integration**: All flashcard operations now properly integrated with Supabase backend

## 📋 API Endpoints

### Health Check
#### GET `/api/health`
Check server status and health.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "production",
  "version": "1.0.0"
}
```

### User Credentials Management
#### POST `/api/credentials`
Store user's AI provider credentials securely in encrypted database.

**Authentication:** Required

**Request Body:**
```json
{
  "provider": "openrouter",
  "apiKey": "user-api-key",
  "baseUrl": "https://openrouter.ai/api/v1",
  "extractionModel": "google/gemini-2.5-flash",
  "generationModel": "google/gemini-2.5-pro"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Credentials stored securely"
}
```

#### GET `/api/credentials/:provider`
Get user's stored configuration (without API key).

**Authentication:** Required

**Response:**
```json
{
  "success": true,
  "hasCredentials": true,
  "configuration": {
    "provider": "openrouter",
    "baseUrl": "https://openrouter.ai/api/v1",
    "extractionModel": "google/gemini-2.5-flash",
    "generationModel": "google/gemini-2.5-pro"
  }
}
```

#### DELETE `/api/credentials/:provider`
Delete user's stored credentials.

**Authentication:** Required

**Response:**
```json
{
  "success": true,
  "message": "Credentials deleted successfully"
}
```

### AI Integration
#### POST `/api/flashcards/generate`
Generate flashcards from text content using AI.

**Authentication:** Required

**Request Body:**
```json
{
  "textContent": "Your study material text...",
  "documentId": "uuid-string",
  "deckTitle": "Optional deck title",
  "count": 10,
  "customPrompt": "Optional custom prompt"
}
```

**Note:** AI credentials are retrieved from secure backend storage automatically. Users must configure their AI provider credentials via the settings page before using AI generation features.

**Response:**
```json
{
  "success": true,
  "flashcards": [
    {
      "question": "What is the capital of France?",
      "answer": "Paris"
    }
  ],
  "deckId": "generated-deck-id"
}
```

#### POST `/api/extract-and-format`
Extract and format text using AI for better readability.

**Authentication:** Required

**Request Body:**
```json
{
  "rawText": "Unformatted text content...",
  "fileName": "document.pdf",
  "aiSettings": {
    "provider": "openrouter",
    "baseUrl": "https://openrouter.ai/api/v1",
    "apiKey": "user-api-key",
    "extractionModel": "google/gemini-2.5-flash"
  }
}
```

### Document Management
#### POST `/api/documents`
Create a new document.

**Authentication:** Required

**Request Body:**
```json
{
  "title": "Document Title",
  "content": "Document content...",
  "type": "pdf|docx|txt",
  "metadata": {}
}
```

#### POST `/api/documents/secure-upload`
Securely upload a document through the backend.

**Authentication:** Required

**Request Body:**
```json
{
  "fileName": "document.pdf",
  "fileContent": "base64-encoded-content",
  "contentType": "application/pdf"
}
```

### Flashcard Management

#### GET `/api/flashcard-sets`
Get all flashcard sets for the authenticated user with card counts.

**Authentication:** Required

**Response:**
```json
[
  {
    "id": "uuid-string",
    "name": "Set Name",
    "description": "Set description",
    "user_id": "user-uuid",
    "study_document_id": "document-uuid",
    "created_at": "2025-01-01T00:00:00.000Z",
    "updated_at": "2025-01-01T00:00:00.000Z",
    "card_count": 10
  }
]
```

#### GET `/api/flashcard-sets/:setId`
Get a specific flashcard set.

**Authentication:** Required

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Set Name",
  "description": "Set description",
  "user_id": "user-uuid",
  "study_document_id": "document-uuid",
  "created_at": "2025-01-01T00:00:00.000Z",
  "updated_at": "2025-01-01T00:00:00.000Z"
}
```

#### GET `/api/flashcard-sets/:setId/due`
Get flashcards that are due for review in a specific set.

**Authentication:** Required

**Response:**
```json
[
  {
    "id": "uuid-string",
    "front_text": "Question text",
    "back_text": "Answer text",
    "set_id": "set-uuid",
    "user_id": "user-uuid",
    "created_at": "2025-01-01T00:00:00.000Z",
    "updated_at": "2025-01-01T00:00:00.000Z"
  }
]
```

#### POST `/api/flashcard-sets`
Create a new flashcard set with flashcards.

**Authentication:** Required

**Request Body:**
```json
{
  "name": "New Set",
  "description": "Set description",
  "study_document_id": "document-uuid",
  "flashcards": [
    {
      "front_text": "Question text",
      "back_text": "Answer text"
    }
  ]
}
```

#### GET `/api/decks` (Legacy)
Get all flashcard decks for the authenticated user.

**Authentication:** Required

**Response:**
```json
[
  {
    "id": 1,
    "name": "Deck Name",
    "description": "Deck description",
    "userId": "user-id",
    "createdAt": 1640995200000
  }
]
```

#### POST `/api/decks`
Create a new flashcard deck.

**Authentication:** Required

**Request Body:**
```json
{
  "name": "New Deck",
  "description": "Deck description"
}
```

#### GET `/api/decks/:deckId/flashcards`
Get all flashcards in a specific deck.

**Authentication:** Required

**Response:**
```json
[
  {
    "id": 1,
    "question": "Question text",
    "answer": "Answer text",
    "deckId": 1,
    "createdAt": 1640995200000
  }
]
```

#### POST `/api/decks/:deckId/flashcards`
Add a new flashcard to a deck.

**Authentication:** Required

**Request Body:**
```json
{
  "question": "Question text",
  "answer": "Answer text"
}
```

### Quiz Management
#### GET `/api/quizzes`
Get all quizzes for the authenticated user.

**Authentication:** Required

#### POST `/api/quizzes/generate`
Generate a quiz from document content.

**Authentication:** Required

**Request Body:**
```json
{
  "textContent": "Source material...",
  "documentId": "uuid-string",
  "quizTitle": "Quiz Title",
  "numberOfQuestions": 10,
  "aiConfig": {
    "provider": "openrouter",
    "baseUrl": "https://openrouter.ai/api/v1",
    "apiKey": "user-api-key",
    "model": "google/gemini-2.5-pro"
  }
}
```

#### GET `/api/quizzes/:quizId`
Get a specific quiz with its questions.

**Authentication:** Required

#### PATCH `/api/quizzes/questions/:questionId/srs`
Update SRS (Spaced Repetition System) data for a quiz question.

**Authentication:** Required

**Request Body:**
```json
{
  "srs_level": 2,
  "due_at": "2024-01-02T00:00:00.000Z",
  "last_reviewed_at": "2024-01-01T00:00:00.000Z",
  "srs_interval": 3,
  "srs_ease_factor": 2.5,
  "srs_repetitions": 1,
  "srs_correct_streak": 1
}
```

## 🚨 Error Responses

### Standard Error Format
```json
{
  "error": "Error message",
  "message": "Detailed error description",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## 🔒 Security Considerations

### API Key Handling
- User AI provider credentials are handled ephemerally
- No persistent storage of user API keys
- Keys are used in-memory only for the duration of the request
- No logging of sensitive credentials

### Rate Limiting
- Consider implementing rate limiting for production
- Monitor API usage patterns
- Protect against abuse and excessive usage

### Input Validation
- All inputs validated using Zod schemas
- SQL injection prevention with parameterized queries
- XSS prevention with proper output encoding

## 📊 Response Times
- Target response time: < 500ms for most endpoints
- AI generation endpoints may take longer (5-30 seconds)
- Health check should respond in < 100ms
